package com.dzhp.permit.routers.llm

import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.json.*
import org.slf4j.LoggerFactory
import java.util.UUID

/**
 * 代理流程路由
 * 提供流式文本接口，将请求转发到外部服务
 *
 * - POST /api/run/flow/permit/mee : 流式文本接口，需要user-jwt认证
 */
fun Application.agentFlowRouter() {
    val logger = LoggerFactory.getLogger("AgentFlowRouter")
    val baseUrl = environment.config.property("api.agentFlow.url").getString()

    // 创建HTTP客户端
    val httpClient = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
                prettyPrint = false
            })
        }
        // 设置超时时间
        engine {
            requestTimeout = 0 // 无超时时间，允许长连接
        }
    }

    routing {
        authenticate("user-llm-jwt") {
            route("/api/run/flow/permit") {
                /**
                 * 流式文本接口
                 * 将请求转发到外部服务，并将响应作为流式文本返回
                 */
                post("/mee") {
                    try {
                        // 生成UUID4作为请求ID
                        val requestId = UUID.randomUUID().toString()

                        // 获取请求体
                        val requestBody = call.receiveText()

                        // 从 JWT 中提取用户ID用于日志
                        val userId = call.principal<JWTPrincipal>()?.getClaim("code", String::class)?.toString() ?: "unknown"

                        logger.info("收到流式文本请求: request_id=$requestId, user_id=$userId, body=${requestBody.take(100)}...")

                        // 设置响应头
                        call.response.header(HttpHeaders.CacheControl, "no-cache")
                        call.response.header(HttpHeaders.Connection, "keep-alive")

                        // 创建流式响应
                        call.respondTextWriter(contentType = ContentType.Text.EventStream) {
                            try {
                                // 尝试将requestId和userId添加到请求中
                                val jsonBody = try {
                                    val jsonElement = Json.parseToJsonElement(requestBody)
                                    if (jsonElement is JsonObject) {
                                        jsonElement.toMutableMap().apply {
                                            put("request_id", JsonPrimitive(requestId))
                                            put("user_id", JsonPrimitive(userId)) // 使用JWT中的code作为userId
                                        }
                                    } else {
                                        null
                                    }
                                } catch (e: Exception) {
                                    logger.warn("无法解析JSON请求体添加requestId和userId: request_id=$requestId, user_id=$userId, error=${e.message}")
                                    null
                                }

                                // 发送请求到外部服务
                                val response = httpClient.post(baseUrl) {
                                    contentType(ContentType.Application.Json)
                                    if (jsonBody != null) {
                                        setBody(JsonObject(jsonBody))
                                    } else {
                                        setBody(requestBody)
                                    }
                                    // 添加requestId和userId到请求头
                                    header("X-Request-ID", requestId)
                                    header("X-User-ID", userId)
                                }

                                // 直接转发响应体，不做任何修改
                                val responseText = response.bodyAsText()
                                try {
                                    write(responseText)
                                    flush()
                                } catch (e: Exception) {
                                    logger.error("发送消息失败: request_id=$requestId, user_id=$userId, error=${e.message}", e)
                                }

                                logger.info("流式文本请求处理完成: request_id=$requestId, user_id=$userId")
                            } catch (e: Exception) {
                                // 仅记录错误日志，不添加任何错误消息到响应中
                                logger.error("流式文本请求处理失败: request_id=$requestId, user_id=$userId, error=${e.message}", e)
                            }
                        }
                    } catch (e: Exception) {
                        val errorRequestId = UUID.randomUUID().toString() // 如果在这个层面出错，创建新的requestId
                        logger.error("流式文本请求处理失败: request_id=$errorRequestId, error=${e.message}", e)
                        call.respondText(
                            text = "错误: ${e.message}",
                            contentType = ContentType.Text.Plain,
                            status = HttpStatusCode.InternalServerError
                        )
                    }
                }
            }
        }
    }
}
