package com.dzhp.permit.models

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.Table

/**
 * 管理员密钥数据结构
 * 一级邀请码，用于管理员创建和管理二级邀请码
 */
@Serializable
data class AdminKey(
    val id: Long = 0,                       // 主键ID
    val code: String = "",                       // 管理员密钥，格式: 地区邮编(6位)+随机数字(4位)
    val maxCNYLimit: Double,                // 最大金额限制（元）
    val status: AdminKeyStatus = AdminKeyStatus.ACTIVE,             // 状态：有效/停用
    val accountType: AccountType = AccountType.FORMAL,              // 账号性质：试用/正式

    // 地域信息
    val province: String? = null,           // 省份
    val city: String? = null,               // 城市
    val district: String? = null,           // 区县（可选）

    // 单位和联系信息
    val companyName: String,                // 所属单位名称
    val contractNumber: String? = null,     // 合同号（可选）
    val contactPerson: String? = null,      // 联系人（可选）
    val contactPhone: String? = null,       // 联系电话（可选）
    val contactEmail: String? = null,       // 联系邮箱（可选）

    val expiresAt: Long,                    // 有效期截止时间（毫秒时间戳）

    val createBy: String? = null,           // 创建人
    val updateBy: String? = null,           // 更新人
    val deleted: Boolean = false,           // 是否删除 0-未删除 1-已删除

    val createdAt: Long = System.currentTimeMillis(),  // 创建时间
    val updatedAt: Long = System.currentTimeMillis(),  // 更新时间
)

/**
 * 管理员密钥状态枚举
 */
@Serializable
enum class AdminKeyStatus {
    ACTIVE,     // 有效
    DISABLED    // 停用
}

/**
 * 账号性质枚举
 */
@Serializable
enum class AccountType {
    TRIAL,      // 试用账号
    FORMAL      // 正式账号
}

/**
 * 管理员密钥表
 * 存储管理员密钥信息
 */
object AdminKeys : Table("admin_key_info") {
    /** 主键 */
    val id = long("id").autoIncrement()

    /** 管理员密钥，唯一索引 */
    val code = varchar("code", length = 64).uniqueIndex()

    /** 最大金额限制（元） */
    val maxCNYLimit = double("max_cny_limit")

    /** 状态：ACTIVE / DISABLED */
    val status = enumerationByName("status", 16, AdminKeyStatus::class)

    /** 账号性质：TRIAL / FORMAL */
    val accountType = enumerationByName("account_type", 16, AccountType::class)

    /** 地域信息 */
    val province = varchar("province", 32).nullable()
    val city = varchar("city", 32).nullable()
    val district = varchar("district", 32).nullable()

    /** 单位和联系信息 */
    val companyName = varchar("company_name", 128)
    val contractNumber = varchar("contract_number", 64).nullable()
    val contactPerson = varchar("contact_person", 64).nullable()
    val contactPhone = varchar("contact_phone", 32).nullable()
    val contactEmail = varchar("contact_email", 64).nullable()

    /** 有效期截止时间 */
    val expiresAt = long("expires_at")

    /** 创建 / 更新信息，可为空 */
    val createBy = varchar("create_by", 64).nullable()
    val updateBy = varchar("update_by", 64).nullable()

    /** 软删除标记 */
    val deleted = bool("deleted").default(false)

    /** 时间戳字段（毫秒） */
    val createdAt = long("created_at")
    val updatedAt = long("updated_at")

    /** 主键声明 */
    override val primaryKey = PrimaryKey(id, name = "PK_admin_key_id")
}
