ktor:
  application:
    modules:
      - com.dzhp.permit.ApplicationKt.module
  deployment:
    port: 18019
  environment: test

jwt:
  realm: "ReviewPollution"
  secret: "NBjBrz'$^h@CmWBBQJgcPy`[tLiz`$X3"
  audience: "permit-api"
  domain: "https://permit.51dzhp.com/"

mysql:
  jdbcUrl: "*****************************************************************************************************************************"
  driverClassName: "com.mysql.cj.jdbc.Driver"
  username: "review_pollution"
  password: "1_52Dzhp"

redis:
  host: "r-bp1opo63stxdgif2fhpd.redis.rds.aliyuncs.com"
  port: "6379"
  password: "1_52Dzhp"
  database: "0"
  timeout: "2000"
  pool:
    maxTotal: "16"
    maxIdle: "8"
    minIdle: "4"


# LDAP认证配置
ldap:
  host: "ldap.52dzhp.com"
  port: 389
  bindDn: "cn=admin,dc=52dzhp,dc=com"
  password: "1allforone"
  base: "ou=people,dc=52dzhp,dc=com"

# 外部MySQL认证配置
external:
  mysql:
    host: "sh-cdb-o2k81s6i.sql.tencentcdb.com"
    port: 58502
    user: "read_user"
    password: "@Bnep9728_"
    database: "bnep_base"
    charset: "utf8mb4"

elasticsearch:
  host: "ai.frp.52dzhp.com"
  port: "9200"
  scheme: "http"
  username: "elastic"
  password: "1@52dzhp"

wechat:
  appId: "wx0fe4154190184fb4"
  appSecret: "8c3bc36c0cad762f6f7ead20e75b5b05"
  redirectUri: "https://permit.51dzhp.com"

aliyun:
  sms:
    accessKeyId: "LTAI5tRNb4SuoFFiAj8NRDfc"
    accessKeySecret: "******************************"
    signName: "阿里云短信测试"
    templateCode: "SMS_154950909"
    endpoint: "dysmsapi.aliyuncs.com"

api:
  agentFlow:
    url: "https://eia.test52dzhp.com:18018/api/run/flow/permit/mee"

