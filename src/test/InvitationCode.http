### ---------------------------------------------------------------------------------------------
### Environment / variables
@baseUrl = http://localhost:18019
@adminToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************.lCxQ3rx7Q6w0dBcz7mEzRQ_ZzpXVwZrjBW9BcIEGORk
### ---------------------------------------------------------------------------------------------
### Health check: GET /
GET {{baseUrl}}/

### ---------------------------------------------------------------------------------------------
### Create new invitation code: POST /api/new/invitation/code
POST {{baseUrl}}/api/admin/new/invitation/code
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "code": "",
  "userType": "INDIVIDUAL",
  "permissionLevel": "STANDARD",
  "regionConstraint": null,
  "status": "ACTIVE",
  "remainingBalance": 1000.0,
  "concurrentLimit": 5,
  "companyId": "zjjcfz",
  "createBy": "admin",
  "updateBy": "admin",
  "deleted": false,
  "expiresAt": 1111111131536000000
}

### ---------------------------------------------------------------------------------------------
### Create new invitation code with custom code: POST /api/new/invitation/code
POST {{baseUrl}}/api/new/invitation/code
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "code": "TEST123456",
  "userType": "COUNTY_ADMINISTRATION",
  "permissionLevel": "ADVANCED",
  "regionConstraint": "330100",
  "status": "ACTIVE",
  "remainingBalance": 5000.0,
  "concurrentLimit": 10,
  "companyId": "company456",
  "createBy": "admin",
  "updateBy": "admin",
  "deleted": false,
  "createdAt": 1,
  "expiresAt": 31536000000,
  "updateAt": 1
}

### ---------------------------------------------------------------------------------------------
### Get invitation codes by company ID: GET /api/admin/invitation/code/company/{companyId}
GET {{baseUrl}}/api/admin/invitation/code/company/zjjcfz
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### Get invitation codes by company ID with onlyActive parameter: GET /api/admin/invitation/code/company/{companyId}?onlyActive=false
GET {{baseUrl}}/api/admin/invitation/code/company/company456?onlyActive=false
Authorization: Bearer {{adminToken}}
